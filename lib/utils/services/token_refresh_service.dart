import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'api_service.dart';
import 'api_endpoints.dart';
import 'cryptographer.dart';
import 'shared_preferences_helper.dart';

import '../app_logger.dart';

/// Service for handling JWT token refresh functionality
/// Based on the working implementation from flutter_configurable
class TokenRefreshService {
  static TokenRefreshService? _instance;
  Timer? _inactivityTimer;
  static const int _inactivityDuration =
      300; // 5 minutes (300 seconds) - reduced frequency
  bool _isDisposed = false;
  DateTime? _lastRefreshTime;

  // Singleton pattern
  static TokenRefreshService get instance {
    _instance ??= TokenRefreshService._internal();
    return _instance!;
  }

  TokenRefreshService._internal();

  /// Add a static method to reset the singleton instance
  static void reset() {
    _instance?._inactivityTimer?.cancel();
    _instance = null;
  }

  /// Initialize the token refresh service
  void initialize() {
    _isDisposed = false; // Reset disposed flag on initialization
    _resetInactivityTimer();
  }

  /// Reset the inactivity timer - call this on user interaction
  void resetInactivityTimer() {
    if (_isDisposed) return; // Do nothing if the service has been disposed

    // Prevent excessive resets - only reset if enough time has passed since last reset
    final now = DateTime.now();
    if (_lastRefreshTime != null &&
        now.difference(_lastRefreshTime!).inSeconds < 30) {
      // Don't reset timer if less than 30 seconds since last refresh
      return;
    }

    _resetInactivityTimer();
  }

  /// Stop the token refresh service
  void dispose() {
    _inactivityTimer?.cancel();
    _isDisposed = true;
  }

  /// Internal method to reset timer
  void _resetInactivityTimer() {
    if (_isDisposed) return;
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(
      Duration(seconds: _inactivityDuration),
      _sendRefreshTokenRequest,
    );
  }

  /// Send refresh token request to the backend
  /// This is the core implementation from flutter_configurable
  Future<void> _sendRefreshTokenRequest() async {
    if (_isDisposed) return; // Check if disposed before running

    // Prevent excessive refresh requests
    final now = DateTime.now();
    if (_lastRefreshTime != null &&
        now.difference(_lastRefreshTime!).inMinutes < 2) {
      AppLogger.info(
          'TOKEN REFRESH: Skipping refresh - too soon since last refresh');
      // Reset timer for next attempt
      if (!_isDisposed) {
        _resetInactivityTimer();
      }
      return;
    }

    try {
      AppLogger.info('TOKEN REFRESH: Starting token refresh request');
      _lastRefreshTime = now;
      ApiService apiService = ApiService();
      AesEncryption aes = AesEncryption();
      SharedPreferences prefs = await SharedPreferences.getInstance();

      // Check if current token is expired
      String? currentToken = prefs.getString('_utopia');
      if (currentToken != null) {
        bool isExpired = JwtDecoder.isExpired(currentToken);
        AppLogger.info('TOKEN REFRESH: Current token expired: $isExpired');
      } else {
        AppLogger.info('TOKEN REFRESH: No current token found');
      }

      // Get current secret key
      String? secretKey = prefs.getString('_tajemnica');
      if (secretKey == null) {
        AppLogger.error('Secret key is null. Cannot refresh token.');
        return;
      }

      // Get client ID and phone number
      String? clientId = await SharedPreferencesHelper.getClientId();
      String? phoneNumber = prefs.getString('msisdn');
      if (phoneNumber == null) {
        AppLogger.error('Phone number is null. Cannot refresh token.');
        return;
      }

      // Prepare request body (same as flutter_configurable)
      var requestBody = {
        'msisdn': phoneNumber,
        'clientId': clientId,
      };

      // Encrypt the request body
      String plainTextRequestBody = jsonEncode(requestBody);
      String encryptedRequestBody =
          aes.encryptWithBase64Key(plainTextRequestBody, secretKey);

      // Send the refresh token request
      // Note: Backend requires the current token even if expired for refresh
      var response = await apiService.postRequest(
        ApiEndpoints.refreshToken,
        {'hashedBody': encryptedRequestBody},
      );

      // Extract and save the new JWT token
      String token = response['znak'];
      await prefs.setString('_utopia', token);

      // Verify token is not expired
      if (JwtDecoder.isExpired(token)) {
        AppLogger.warning(
            'TOKEN REFRESH: Warning - Received token is already expired!');
        return;
      }

      // Decode JWT and extract new secret key
      Map<String, dynamic> decodedClaims = JwtDecoder.decode(token);
      String newTajemnica = decodedClaims['tajemnica'];
      await prefs.setString('_tajemnica', newTajemnica);

      AppLogger.info(
          'TOKEN REFRESH: Successfully refreshed token and secret key');

      // Reset the timer for the next refresh
      if (!_isDisposed) {
        // Check if disposed before resetting timer
        _resetInactivityTimer();
      }
    } catch (e) {
      AppLogger.error('TOKEN REFRESH ERROR: Failed to refresh token: $e');

      // Check if this is an authentication error (403, 401, etc.)
      String errorString = e.toString();
      if (errorString.contains('HTTP 403') ||
          errorString.contains('HTTP 401') ||
          errorString.contains('403') ||
          errorString.contains('401')) {
        AppLogger.warning(
            'TOKEN REFRESH: Authentication error detected - token may be completely invalid');
        // In this case, the user might need to log in again
        // We could potentially trigger a logout here, but for now just log it
      }

      // Don't reset timer on error - this allows for retry on next user interaction
      // But we should probably implement exponential backoff in production
    }
  }

  /// Check if current token is expired
  Future<bool> isTokenExpired() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('_utopia');

      if (token == null) {
        return true; // No token means expired
      }

      return JwtDecoder.isExpired(token);
    } catch (e) {
      AppLogger.error('checking token expiration: $e');
      return true; // Assume expired on error
    }
  }

  /// Force refresh token now (for manual refresh)
  Future<bool> forceRefreshToken() async {
    try {
      AppLogger.info('TOKEN REFRESH: Force refresh token requested');
      await _sendRefreshTokenRequest();
      AppLogger.info(
          'TOKEN REFRESH: Force refresh token completed successfully');
      return true;
    } catch (e) {
      AppLogger.error('TOKEN REFRESH: Force refresh token failed: $e');
      return false;
    }
  }

  /// Get token expiration info
  Future<Map<String, dynamic>?> getTokenInfo() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('_utopia');

      if (token == null) {
        return null;
      }

      Map<String, dynamic> decodedClaims = JwtDecoder.decode(token);
      DateTime? expirationDate = JwtDecoder.getExpirationDate(token);
      bool isExpired = JwtDecoder.isExpired(token);

      return {
        'isExpired': isExpired,
        'expirationDate': expirationDate,
        'claims': decodedClaims,
        'timeUntilExpiry': expirationDate.difference(DateTime.now()),
      };
    } catch (e) {
      AppLogger.error('getting token info: $e');
      return null;
    }
  }
}
