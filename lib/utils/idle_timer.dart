import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import '../configuration/client_config.dart';
import '../screens/login_screen.dart';
import 'services/token_refresh_service.dart';
import 'services/account_manager.dart';
import 'services/auth_service.dart';
import 'services/shared_preferences_helper.dart';

import 'app_logger.dart';

class IdleTimerService {
  static final IdleTimerService _instance = IdleTimerService._internal();
  static final bool _debug = false;

  DateTime? _lastActivityTime;
  Timer? _backgroundCheckTimer;
  bool _timeoutOccurredInBackground = false;
  bool _isDialogShowing = false;

  bool get hasTimedOutInBackground => _timeoutOccurredInBackground;

  factory IdleTimerService({VoidCallback? onTimeout}) {
    if (onTimeout != null) {
      _instance.onTimeout = onTimeout;
    }
    return _instance;
  }

  IdleTimerService._internal();

  static const defaultIdleDuration = Duration(minutes: 5);
  static const countdownDuration = Duration(seconds: 30);
  static const backgroundCheckInterval = Duration(seconds: 5);

  Timer? _idleTimer;
  Timer? _countdownTimer;
  int _countdownValue = countdownDuration.inSeconds;
  VoidCallback? onTimeout;
  bool _isEnabled = false;
  Duration? _cachedIdleDuration;

  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  bool get isEnabled => _isEnabled;
  bool _isHandlingLifecycleChange = false;

  void handleAppLifecycleState(AppLifecycleState state) async {
    if (_isHandlingLifecycleChange) return;
    _isHandlingLifecycleChange = true;

    try {
      switch (state) {
        case AppLifecycleState.paused:
        case AppLifecycleState.inactive:
        case AppLifecycleState.hidden:
          if (_isEnabled && _lastActivityTime != null) {
            if (!_isDialogShowing) {
              _startBackgroundCheck();
            }
          }
          break;
        case AppLifecycleState.resumed:
          _backgroundCheckTimer?.cancel();
          if (_isEnabled) {
            if (_isDialogShowing) {
              _timeoutOccurredInBackground = false;
              break;
            }
            if (_timeoutOccurredInBackground) {
              _performTimeout();
              break;
            }
            if (_lastActivityTime != null) {
              final now = DateTime.now();
              final idleDuration = await _getIdleDuration();
              final elapsedTime = now.difference(_lastActivityTime!);
              if (elapsedTime >= idleDuration) {
                _showIdleDialog();
              } else {
                await _startIdleTimer();
              }
            }
          }
          break;
        case AppLifecycleState.detached:
          break;
      }
    } finally {
      _isHandlingLifecycleChange = false;
    }
  }

  void _startBackgroundCheck() {
    _backgroundCheckTimer?.cancel();
    _backgroundCheckTimer =
        Timer.periodic(backgroundCheckInterval, (timer) async {
      if (!_isEnabled || _lastActivityTime == null || _isDialogShowing) {
        timer.cancel();
        return;
      }
      final now = DateTime.now();
      final idleDuration = _cachedIdleDuration ?? await _getIdleDuration();
      _cachedIdleDuration = idleDuration;
      final elapsedTime = now.difference(_lastActivityTime!);
      if (elapsedTime >= idleDuration) {
        _timeoutOccurredInBackground = true;
        timer.cancel();
      }
    });
  }

  Future<Duration> _getIdleDuration() async {
    if (_cachedIdleDuration != null) return _cachedIdleDuration!;

    try {
      // Get timeout from the central client configuration
      if (_debug) {
        AppLogger.info(
            'IdleTimer: Attempting to load duration from ClientConfig...');
      }
      final timeoutInMillis =
          ClientThemeManager().currentClientConfig.appIdleTimeout;
      _cachedIdleDuration = Duration(milliseconds: timeoutInMillis);
      if (_debug) {
        AppLogger.info(
            'IdleTimer: SUCCESS! Loaded idle duration from ClientConfig: ${timeoutInMillis}ms');
      }
      return _cachedIdleDuration!;
    } catch (e) {
      if (_debug) {
        AppLogger.info(
            'IdleTimer: FAILED to load from ClientConfig: $e. FALLING BACK TO DEFAULT.');
      }
    }

    _cachedIdleDuration = defaultIdleDuration;
    if (_debug) {
      AppLogger.info(
          'IdleTimer: Using default idle duration: ${defaultIdleDuration.inSeconds}s');
    }
    return defaultIdleDuration;
  }

  void disableIdleTimer({String reason = 'No reason provided'}) {
    if (!_isEnabled) return;
    if (_debug) AppLogger.info('IdleTimer: Disabling timer. Reason: $reason');
    _isEnabled = false;
    _lastActivityTime = null;
    _idleTimer?.cancel();
    _idleTimer = null;
    _backgroundCheckTimer?.cancel();
    _backgroundCheckTimer = null;
  }

  void dispose() {
    if (_debug) AppLogger.info('IdleTimer: Disposing service instance.');
    _idleTimer?.cancel();
    _backgroundCheckTimer?.cancel();
    _countdownTimer?.cancel();
    _idleTimer = null;
    _backgroundCheckTimer = null;
    _countdownTimer = null;
    _lastActivityTime = null;
    _cachedIdleDuration = null;
    _isDialogShowing = false;
    _isEnabled = false;
    onTimeout = null;
  }

  void enableIdleTimer({String reason = 'No reason provided'}) async {
    if (_isEnabled) {
      if (_debug) {
        AppLogger.info(
            'IdleTimer: Timer already enabled. Resetting. Reason: $reason');
      }
      resetTimer();
      return;
    }
    _isEnabled = true;
    _timeoutOccurredInBackground = false;
    resetTimer();
  }

  void resetTimer() async {
    if (!_isEnabled) return;
    _lastActivityTime = DateTime.now();
    _idleTimer?.cancel();
    _timeoutOccurredInBackground = false;
    await _startIdleTimer();
  }

  Future<void> _startIdleTimer() async {
    if (!_isEnabled) return;
    _idleTimer?.cancel();
    final idleDuration = await _getIdleDuration();
    if (_debug) {
      AppLogger.info(
          'IdleTimer: Starting timer with duration: ${idleDuration.inSeconds} seconds.');
    }
    _idleTimer = Timer(idleDuration, _showIdleDialog);
  }

  void _showIdleDialog() {
    Future.delayed(Duration.zero, () {
      if (_debug) {
        AppLogger.info('IdleTimer: _showIdleDialog called. Timer has fired!');
      }
      if (_isDialogShowing) {
        if (_debug) {
          AppLogger.info(
              'IdleTimer: FAILED to show dialog. Another dialog is already showing.');
        }
        return;
      }
      _isDialogShowing = true;
      _idleTimer?.cancel();

      final context = navigatorKey.currentContext;
      if (context == null) {
        if (_debug) {
          AppLogger.info(
              'IdleTimer: FAILED to show dialog. navigatorKey.currentContext is null.');
        }
        _isDialogShowing = false;
        return;
      }

      if (_debug) {
        AppLogger.info(
            'IdleTimer: SUCCESS! Conditions met, showing timeout dialog now.');
      }
      _countdownValue = countdownDuration.inSeconds;
      final colors = ClientThemeManager().colors;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return StatefulBuilder(
            builder: (context, setState) {
              _countdownTimer ??=
                  Timer.periodic(const Duration(seconds: 1), (timer) {
                if (_countdownValue > 0) {
                  setState(() {
                    _countdownValue--;
                  });
                } else {
                  timer.cancel();
                  _countdownTimer = null;

                  // Use the global navigator key to safely pop the dialog.
                  // Check if the context is still mounted before popping.
                  final navContext = navigatorKey.currentContext;
                  if (navContext != null && navContext.mounted) {
                    Navigator.of(navContext).pop();
                  }

                  _performTimeout();
                }
              });

              return BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                child: AlertDialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  backgroundColor: Colors.white,
                  title: Row(
                    children: [
                      Icon(Icons.timer_outlined, color: colors.primary),
                      const SizedBox(width: 8),
                      Text('Session Timeout',
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: colors.textPrimary)),
                    ],
                  ),
                  content: Text(
                    'You will be logged out in $_countdownValue seconds due to inactivity.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: colors.textPrimary.withValues(alpha: 0.8)),
                  ),
                  actionsAlignment: MainAxisAlignment.center,
                  actions: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colors.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                        ),
                        child: const Text('Stay Logged In',
                            style: TextStyle(color: Colors.white)),
                        onPressed: () async {
                          _countdownTimer?.cancel();
                          _countdownTimer = null;
                          _isDialogShowing = false;
                          Navigator.of(dialogContext).pop();

                          // Check token status and refresh when user chooses to stay logged in
                          AppLogger.info(
                              'IDLE TIMER: User chose to stay logged in - checking token status');
                          try {
                            bool isExpired = await TokenRefreshService.instance
                                .isTokenExpired();
                            AppLogger.info(
                                'IDLE TIMER: Token expired status: $isExpired');

                            if (isExpired) {
                              AppLogger.warning(
                                  'Token is expired - redirecting to login');
                              // Token is expired, we can't refresh it. User needs to log in again.
                              _performTimeout();
                              return; // Exit early, don't reset timer
                            } else {
                              AppLogger.info(
                                  'Token is still valid - attempting refresh');
                              bool refreshSuccess = await TokenRefreshService
                                  .instance
                                  .forceRefreshToken();
                              if (refreshSuccess) {
                                AppLogger.info(
                                    'Token refreshed successfully after stay logged in');
                              } else {
                                AppLogger.warning(
                                    'Token refresh failed after stay logged in');
                              }
                            }
                          } catch (e) {
                            AppLogger.error(
                                'Error checking/refreshing token after stay logged in: $e');
                          }

                          resetTimer();
                        },
                      ),
                    )
                  ],
                ),
              );
            },
          );
        },
      ).whenComplete(() {
        _countdownTimer?.cancel();
        _countdownTimer = null;
        _isDialogShowing = false;
      });
    });
  }

  void _performTimeout() {
    if (_debug) {
      AppLogger.info('IdleTimer: Timeout occurred! Performing logout.');
    }

    // Cancel all timers
    _idleTimer?.cancel();
    _countdownTimer?.cancel();
    _backgroundCheckTimer?.cancel();

    // Reset service state
    _isEnabled = false;
    _lastActivityTime = null;
    _isDialogShowing = false;
    _timeoutOccurredInBackground = false;
    _cachedIdleDuration = null;

    // Clean up other services
    TokenRefreshService.instance.dispose();
    TokenRefreshService.reset();

    // Clear all cached data before logout
    _clearAllCachedData();

    // Trigger the timeout callback if provided
    if (onTimeout != null) {
      onTimeout!();
    } else {
      // Default navigation if no callback is set
      final navContext = navigatorKey.currentContext;
      if (navContext != null) {
        Navigator.of(navContext).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    }
  }

  void clearBackgroundTimeoutFlag() {
    _timeoutOccurredInBackground = false;
  }

  /// Clear all cached data
  Future<void> _clearAllCachedData() async {
    try {
      await AccountManager.clearCachedAccounts();
      await AuthService().logout();
      await SharedPreferencesHelper.clearSharedPreferences();
    } catch (e) {
      AppLogger.error('Error clearing cached data during logout: $e');
    }
  }
}
