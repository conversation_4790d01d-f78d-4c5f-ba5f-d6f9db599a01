import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../configuration/client_config.dart';

/// Enumeration of supported font families
enum SupportedFont {
  inter,
  roboto,
  poppins,
  openSans,
  lato,
  sourceSansPro,
  nunitoSans,
  system, // Uses system default
}

/// Helper class for font configuration across the app
class FontHelper {
  // Private constructor to prevent instantiation
  FontHelper._();

  /// Get the current client's font family name
  static String get currentFontFamily {
    try {
      final clientConfig = ClientThemeManager().currentClientConfig;
      return clientConfig.fontFamily ?? _getGoogleFontFamily(SupportedFont.inter);
    } catch (e) {
      return _getGoogleFontFamily(SupportedFont.inter);
    }
  }

  /// Get Google Font family name for a specific font
  static String _getGoogleFontFamily(SupportedFont font) {
    switch (font) {
      case SupportedFont.inter:
        return GoogleFonts.inter().fontFamily!;
      case SupportedFont.roboto:
        return GoogleFonts.roboto().fontFamily!;
      case SupportedFont.poppins:
        return GoogleFonts.poppins().fontFamily!;
      case SupportedFont.openSans:
        return GoogleFonts.openSans().fontFamily!;
      case SupportedFont.lato:
        return GoogleFonts.lato().fontFamily!;
      case SupportedFont.sourceSansPro:
        return GoogleFonts.sourceSerif4().fontFamily!;
      case SupportedFont.nunitoSans:
        return GoogleFonts.nunitoSans().fontFamily!;
      case SupportedFont.system:
        return 'System';
    }
  }

  /// Get font family for a specific supported font
  static String getFontFamily(SupportedFont font) {
    return _getGoogleFontFamily(font);
  }

  /// Get the Inter font family name for use in client configurations
  static String get interFontFamily => _getGoogleFontFamily(SupportedFont.inter);
  
  /// Get the Roboto font family name for use in client configurations
  static String get robotoFontFamily => _getGoogleFontFamily(SupportedFont.roboto);
  
  /// Get the Poppins font family name for use in client configurations
  static String get poppinsFontFamily => _getGoogleFontFamily(SupportedFont.poppins);

  /// Get Inter TextStyle with optional parameters
  static TextStyle getInterTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
  }) {
    return GoogleFonts.inter(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      letterSpacing: letterSpacing,
      height: height,
    );
  }

  /// Get Inter TextStyle for headings
  static TextStyle getInterHeading1({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: color,
    );
  }

  /// Get Inter TextStyle for headings
  static TextStyle getInterHeading2({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      color: color,
    );
  }

  /// Get Inter TextStyle for headings
  static TextStyle getInterHeading3({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: color,
    );
  }

  /// Get Inter TextStyle for body text
  static TextStyle getInterBodyLarge({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 16,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  /// Get Inter TextStyle for body text
  static TextStyle getInterBodyMedium({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 14,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  /// Get Inter TextStyle for small text
  static TextStyle getInterBodySmall({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 12,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  /// Get Inter TextStyle for button text
  static TextStyle getInterButton({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: color,
    );
  }

  /// Get Inter TextStyle for captions
  static TextStyle getInterCaption({Color? color}) {
    return GoogleFonts.inter(
      fontSize: 12,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  // === CONFIGURABLE FONT METHODS ===
  // These methods use the current client's configured font

  /// Get configurable TextStyle with optional parameters using current client font
  static TextStyle getTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
  }) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      letterSpacing: letterSpacing,
      height: height,
    );
  }

  /// Get configurable heading 1 style using current client font
  static TextStyle getHeading1({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: color,
    );
  }

  /// Get configurable heading 2 style using current client font
  static TextStyle getHeading2({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 24,
      fontWeight: FontWeight.w600,
      color: color,
    );
  }

  /// Get configurable heading 3 style using current client font
  static TextStyle getHeading3({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: color,
    );
  }

  /// Get configurable body large style using current client font
  static TextStyle getBodyLarge({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  /// Get configurable body medium style using current client font
  static TextStyle getBodyMedium({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 14,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  /// Get configurable body small style using current client font
  static TextStyle getBodySmall({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  /// Get configurable button style using current client font
  static TextStyle getButtonStyle({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: color,
    );
  }

  /// Get configurable caption style using current client font
  static TextStyle getCaption({Color? color}) {
    return _getStyleForFont(
      currentFontFamily,
      fontSize: 12,
      fontWeight: FontWeight.normal,
      color: color,
    );
  }

  /// Internal method to get appropriate Google Font style
  static TextStyle _getStyleForFont(
    String fontFamily, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
  }) {
    try {
      // Try to match the font family to a Google Font
      if (fontFamily.contains('Inter')) {
        return GoogleFonts.inter(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      } else if (fontFamily.contains('Roboto')) {
        return GoogleFonts.roboto(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      } else if (fontFamily.contains('Poppins')) {
        return GoogleFonts.poppins(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      } else if (fontFamily.contains('OpenSans') || fontFamily.contains('Open Sans')) {
        return GoogleFonts.openSans(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      } else if (fontFamily.contains('Lato')) {
        return GoogleFonts.lato(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      } else if (fontFamily.contains('SourceSansPro') || fontFamily.contains('Source Sans Pro')) {
        return GoogleFonts.sourceSerif4(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      } else if (fontFamily.contains('NunitoSans') || fontFamily.contains('Nunito Sans')) {
        return GoogleFonts.nunitoSans(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      } else {
        // Fallback to Inter if font not recognized
        return GoogleFonts.inter(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          letterSpacing: letterSpacing,
          height: height,
        );
      }
    } catch (e) {
      // Fallback to basic TextStyle if Google Fonts fails
      return TextStyle(
        fontFamily: fontFamily,
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        letterSpacing: letterSpacing,
        height: height,
      );
    }
  }
}
