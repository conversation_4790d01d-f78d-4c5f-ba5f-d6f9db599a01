import 'package:flutter/material.dart';
import '../utils/font_helper.dart';
import '../configuration/client_config.dart';

/// Example showing how to use the Inter font implementation
/// This file demonstrates various ways to apply Inter font in your app
class FontUsageExample extends StatelessWidget {
  const FontUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    final clientConfig = ClientThemeManager().currentClientConfig;
    final colors = ClientThemeManager().colors;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Inter Font Examples',
          style: FontHelper.getInterHeading2(color: Colors.white),
        ),
        backgroundColor: colors.primary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Using FontHelper methods
            Text(
              'Heading 1 with Inter',
              style: FontHelper.getInterHeading1(color: colors.textPrimary),
            ),
            const SizedBox(height: 16),
            
            Text(
              'Heading 2 with Inter',
              style: FontHelper.getInterHeading2(color: colors.textPrimary),
            ),
            const SizedBox(height: 16),
            
            Text(
              'Heading 3 with Inter',
              style: FontHelper.getInterHeading3(color: colors.textPrimary),
            ),
            const SizedBox(height: 16),
            
            Text(
              'This is body large text using Inter font. It demonstrates how the Inter font looks in your financial application. Inter is designed specifically for user interfaces and provides excellent readability.',
              style: FontHelper.getInterBodyLarge(color: colors.textPrimary),
            ),
            const SizedBox(height: 16),
            
            Text(
              'This is body medium text using Inter font. Perfect for descriptions and secondary content.',
              style: FontHelper.getInterBodyMedium(color: colors.textSecondary),
            ),
            const SizedBox(height: 16),
            
            Text(
              'This is small body text using Inter font.',
              style: FontHelper.getInterBodySmall(color: colors.textSecondary),
            ),
            const SizedBox(height: 16),
            
            Text(
              'This is caption text using Inter font.',
              style: FontHelper.getInterCaption(color: colors.textDisabled),
            ),
            const SizedBox(height: 24),
            
            // Button example
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: colors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text(
                'Button with Inter',
                style: FontHelper.getInterButton(color: Colors.white),
              ),
            ),
            const SizedBox(height: 24),
            
            // Card example
            Card(
              color: colors.surface,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Financial Summary',
                      style: FontHelper.getInterHeading3(color: colors.textPrimary),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Account Balance',
                      style: FontHelper.getInterBodyMedium(color: colors.textSecondary),
                    ),
                    Text(
                      'KSH 125,450.75',
                      style: FontHelper.getInterHeading2(color: colors.primary),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Last updated: 2 minutes ago',
                      style: FontHelper.getInterCaption(color: colors.textDisabled),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Custom styling example
            Text(
              'Custom Inter Styling',
              style: FontHelper.getInterTextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: colors.secondary,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 16),
            
            // Information box
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '💡 Inter Font Benefits',
                    style: FontHelper.getInterHeading3(color: colors.info),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Designed specifically for digital interfaces\n'
                    '• Optimized for screen readability\n'
                    '• Excellent for financial data display\n'
                    '• Supports multiple weights and styles\n'
                    '• Professional and modern appearance',
                    style: FontHelper.getInterBodyMedium(color: colors.textPrimary),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Current client info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: colors.primary),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Configuration',
                          style: FontHelper.getInterBodyMedium(color: colors.primary),
                        ),
                        Text(
                          'Client: ${clientConfig.displayName}',
                          style: FontHelper.getInterBodySmall(color: colors.textSecondary),
                        ),
                        Text(
                          'Font Family: ${clientConfig.fontFamily}',
                          style: FontHelper.getInterBodySmall(color: colors.textSecondary),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
