import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/color_palette.dart';
import '../configuration/client_config.dart';
import '../utils/sms_autofill_helper.dart';
import '../utils/app_logger.dart';
import '../utils/services/api_service.dart';
import '../utils/services/cryptographer.dart';
import '../utils/services/shared_preferences_helper.dart';
import '../utils/services/api_endpoints.dart';

/// Unified OTP Dialog that handles all OTP verification scenarios
/// including withdrawals, deposits, transfers, utility bills, and other transactions
class UnifiedOtpDialog extends StatefulWidget {
  final String phoneNumber;
  final String transactionType;
  final String? amount;
  final Map<String, String>? transactionDetails;
  final Future<bool> Function(String otp)? onOtpVerified;
  final VoidCallback? onCancel;

  const UnifiedOtpDialog({
    super.key,
    required this.phoneNumber,
    required this.transactionType,
    this.amount,
    this.transactionDetails,
    this.onOtpVerified,
    this.onCancel,
  });

  @override
  State<UnifiedOtpDialog> createState() => _UnifiedOtpDialogState();
}

class _UnifiedOtpDialogState extends State<UnifiedOtpDialog> {
  final ApiService _apiService = ApiService();
  final AesEncryption _aes = AesEncryption();
  final List<TextEditingController> controllers =
      List.generate(4, (index) => TextEditingController());
  final List<FocusNode> focusNodes = List.generate(4, (index) => FocusNode());
  
  bool _isValidating = false;
  bool _isResendEnabled = false;
  bool _isResending = false;
  int _resendCountdown = 30;
  Timer? _resendTimer;
  String? _errorMessage;
  String? _successMessage;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
    _setupAutofillListener();
    _requestSMSPermissions();
    AppLogger.info('Unified OTP dialog initialized for ${widget.transactionType}');
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    for (var controller in controllers) {
      controller.dispose();
    }
    for (var node in focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  /// Request SMS permissions for Android 15+ compatibility
  Future<void> _requestSMSPermissions() async {
    try {
      SmsAutofillHelper.logAutofillDebugInfo('Unified OTP Init');
      
      final permissionsGranted = await SmsAutofillHelper.requestAutofillPermissions();
      
      if (permissionsGranted) {
        AppLogger.info('All SMS autofill permissions granted');
      } else {
        AppLogger.warning('Some SMS autofill permissions denied - autofill may not work on Android 15');
      }
      
      final status = await SmsAutofillHelper.getAutofillStatus();
      AppLogger.info('Final SMS autofill status: $status');
      
    } catch (e) {
      AppLogger.error('Error requesting SMS permissions: $e');
    }
  }

  /// Setup autofill listener for SMS OTP
  void _setupAutofillListener() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        focusNodes[0].requestFocus();
      }
    });
  }

  /// Start resend timer
  void _startResendTimer() {
    setState(() {
      _isResendEnabled = false;
      _resendCountdown = 30;
    });

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_resendCountdown > 0) {
          _resendCountdown--;
        } else {
          _isResendEnabled = true;
          timer.cancel();
        }
      });
    });
  }

  /// Handle OTP field changes with smart navigation
  void _handleOtpFieldChange(String value, int index) {
    // Clear error message when user starts typing
    if (_errorMessage != null) {
      setState(() {
        _errorMessage = null;
      });
    }
    
    if (value.length > 1) {
      // Handle autofill or paste operation - distribute digits across fields
      _handlePastedOtp(value, index);
      return;
    }

    if (value.isNotEmpty) {
      // Move to next field when digit is entered
      if (index < 3) {
        focusNodes[index + 1].requestFocus();
      } else {
        // Auto-submit when last field is filled
        _checkAutoSubmit();
      }
    } else {
      // Handle backspace - move to previous field and clear it
      if (index > 0) {
        focusNodes[index - 1].requestFocus();
        controllers[index - 1].clear();
      }
    }
    
    // Trigger rebuild to update progress indicator
    setState(() {});
  }

  /// Handle pasted OTP codes
  void _handlePastedOtp(String pastedValue, int startIndex) {
    // Extract only digits from pasted content
    String digits = pastedValue.replaceAll(RegExp(r'[^0-9]'), '');
    
    // Clear all fields first
    for (int i = 0; i < 4; i++) {
      controllers[i].clear();
    }
    
    // Fill fields with pasted digits
    for (int i = 0; i < 4 && i < digits.length; i++) {
      controllers[i].text = digits[i];
    }
    
    // Move focus to the appropriate field
    if (digits.length >= 4) {
      focusNodes[3].requestFocus();
      _checkAutoSubmit();
    } else if (digits.isNotEmpty) {
      int nextIndex = digits.length < 4 ? digits.length : 3;
      focusNodes[nextIndex].requestFocus();
    }
    
    // Trigger rebuild to update progress indicator
    setState(() {});
  }

  /// Check if OTP is complete and auto-submit
  void _checkAutoSubmit() {
    String otp = controllers.map((controller) => controller.text).join();
    if (otp.length == 4) {
      // Small delay to show the complete OTP before submitting
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && !_isValidating) {
          _onOtpSubmitted();
        }
      });
    }
  }

  /// Submit OTP for verification
  Future<void> _onOtpSubmitted() async {
    String otp = controllers.map((controller) => controller.text).join();
    if (otp.length != 4) {
      _showErrorMessage('Please enter a valid 4-digit OTP');
      return;
    }

    setState(() {
      _isValidating = true;
    });

    try {
      if (widget.onOtpVerified != null) {
        bool success = await widget.onOtpVerified!(otp);
        if (success) {
          // Close dialog and return OTP
          if (mounted) {
            Navigator.pop(context, otp);
          }
        } else {
          // Error handling is done in the callback, clear fields for retry
          _clearOtpFieldsOnly();
          _showErrorMessage('Invalid OTP. Please try again.');
        }
      } else {
        // Fallback - just return the OTP
        if (mounted) {
          Navigator.pop(context, otp);
        }
      }
    } catch (e) {
      AppLogger.error('OTP validation error: $e');
      _showErrorMessage('An error occurred during verification. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isValidating = false;
        });
      }
    }
  }

  /// Resend OTP
  Future<void> _resendOtp() async {
    if (!_isResendEnabled || _isResending) return;

    setState(() {
      _isResending = true;
    });

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? secretKey = prefs.getString('_tajemnica');
      
      if (secretKey == null) {
        _showErrorMessage('Session expired. Please restart.');
        return;
      }

      String formattedPhone = _formatMsisdn(widget.phoneNumber);
      String? clientId = await SharedPreferencesHelper.getClientId();

      // Encrypt request data
      String encryptedMsisdn = _aes.encryptWithBase64Key(formattedPhone, secretKey);
      String encryptedClientId = _aes.encryptWithBase64Key(clientId ?? '', secretKey);

      var requestBody = {
        'msisdn': encryptedMsisdn,
        'clientId': encryptedClientId,
        'categoryName': 'Spotcash_App',
        'action': 'resend_otp',
      };

      var response = await _apiService.postRequest(
        ApiEndpoints.validateOtp,
        requestBody,
        context: context,
        isAuth: true,
      );

      if (response != null && response['responseCode'] == '00') {
        _showSuccessMessage('OTP has been resent successfully');
        _startResendTimer();
        _clearOtpFields();
      } else {
        _showErrorMessage(response?['responseDescription'] ?? 'Failed to resend OTP');
      }
    } catch (e) {
      _showErrorMessage('Failed to resend OTP. Please try again.');
    } finally {
      setState(() {
        _isResending = false;
      });
    }
  }

  /// Clear all OTP fields
  void _clearOtpFields() {
    for (var controller in controllers) {
      controller.clear();
    }
    setState(() {
      _errorMessage = null;
    });
    focusNodes[0].requestFocus();
  }

  /// Clear OTP fields without clearing error message
  void _clearOtpFieldsOnly() {
    for (var controller in controllers) {
      controller.clear();
    }
    focusNodes[0].requestFocus();
  }

  /// Show error message in the dialog
  void _showErrorMessage(String message) {
    setState(() {
      _errorMessage = message;
    });
    
    // Clear error message after 4 seconds
    Timer(const Duration(seconds: 4), () {
      if (mounted) {
        setState(() {
          _errorMessage = null;
        });
      }
    });
  }

  /// Show success message in the modal
  void _showSuccessMessage(String message) {
    setState(() {
      _errorMessage = null;
      _successMessage = message;
    });
    
    // Clear success message after 3 seconds
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _successMessage = null;
        });
      }
    });
  }

  /// Get appropriate dialog title based on transaction type
  String _getDialogTitle() {
    switch (widget.transactionType.toLowerCase()) {
      case 'withdraw':
      case 'withdrawal':
        return 'Verify Withdrawal';
      case 'deposit':
        return 'Verify Deposit';
      case 'transfer':
      case 'inter account':
        return 'Verify Transfer';
      case 'utility':
      case 'dstv':
      case 'zuku':
      case 'star':
      case 'nwtr':
        return 'Verify Payment';
      default:
        return 'Enter OTP';
    }
  }

  /// Get appropriate instruction message
  String _getInstructionMessage() {
    String baseMessage = 'Please enter the OTP sent to ${widget.phoneNumber}';
    
    // Add transaction-specific context if amount is available
    if (widget.amount != null && widget.amount!.isNotEmpty) {
      return '$baseMessage to verify your ${widget.transactionType.toLowerCase()} of KES ${widget.amount}';
    }
    
    return baseMessage;
  }

  /// Format MSISDN to the correct format
  String _formatMsisdn(String msisdn) {
    // Remove any non-digit characters
    String digits = msisdn.replaceAll(RegExp(r'\D'), '');
    
    // Format according to the existing logic
    if (digits.length == 12 && digits.startsWith('254')) {
      return digits;
    } else if (digits.length == 10 && digits.startsWith('0')) {
      return '254${digits.substring(1)}';
    } else if (digits.length == 9 && (digits.startsWith('7') || digits.startsWith('1'))) {
      return '254$digits';
    }
    
    return digits;
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: Dialog(
        insetPadding: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[900] : Colors.grey[100],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with title and progress indicator
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.unselectedNavItemColor,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _getDialogTitle(),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Progress indicator
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(4, (index) {
                        String currentOtp = controllers.map((c) => c.text).join();
                        bool isFilled = index < currentOtp.length;
                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 2),
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: isFilled 
                                ? Colors.white 
                                : Colors.white.withValues(alpha: 0.3),
                            shape: BoxShape.circle,
                          ),
                        );
                      }),
                    ),
                  ],
                ),
              ),
              
              // Content area
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Instruction message
                    Text(
                      _getInstructionMessage(),
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[400] : Colors.grey[700],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'If SMS fails, check your email for the OTP',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[600],
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    
                    // Error message display
                    if (_errorMessage != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.red.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    
                    // Success message display
                    if (_successMessage != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.green.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              color: Colors.green,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _successMessage!,
                                style: TextStyle(
                                  color: Colors.green,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                    // SMS Autofill status indicator
                    FutureBuilder<PermissionStatus>(
                      future: Permission.sms.status,
                      builder: (context, snapshot) {
                        final hasPermission = snapshot.data?.isGranted ?? false;
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12, 
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: hasPermission 
                              ? Colors.green.withValues(alpha: 0.1)
                              : Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: hasPermission 
                                ? Colors.green.withValues(alpha: 0.3)
                                : Colors.orange.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                hasPermission 
                                  ? Icons.message_rounded 
                                  : Icons.message_outlined,
                                size: 16,
                                color: hasPermission 
                                  ? Colors.green[600] 
                                  : Colors.orange[600],
                              ),
                              const SizedBox(width: 6),
                              Text(
                                hasPermission 
                                  ? 'SMS Auto-read enabled' 
                                  : 'SMS Permission needed',
                                style: TextStyle(
                                  color: hasPermission 
                                    ? Colors.green[600] 
                                    : Colors.orange[600],
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Resend OTP section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Didn\'t receive the code?',
                          style: TextStyle(
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        TextButton(
                          onPressed: (_isResendEnabled && 
                                     !_isResending && 
                                     !_isValidating) ? _resendOtp : null,
                          child: _isResending
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 12,
                                      height: 12,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          ColorPalette.primary,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Resending...',
                                      style: TextStyle(
                                        color: ColorPalette.primary,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  _isResendEnabled 
                                    ? 'Resend OTP' 
                                    : 'Resend OTP ($_resendCountdown)',
                                  style: TextStyle(
                                    color: _isResendEnabled 
                                      ? ColorPalette.primary 
                                      : Colors.grey,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    
                    // OTP input fields with autofill support
                    AutofillGroup(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(
                          4,
                          (index) => SizedBox(
                            width: 50,
                            child: KeyboardListener(
                              focusNode: FocusNode(),
                              onKeyEvent: (KeyEvent event) {
                                if (event is KeyDownEvent) {
                                  if (event.logicalKey == LogicalKeyboardKey.backspace) {
                                    if (controllers[index].text.isEmpty && index > 0) {
                                      focusNodes[index - 1].requestFocus();
                                      controllers[index - 1].clear();
                                    }
                                  }
                                }
                              },
                              child: TextField(
                                controller: controllers[index],
                                focusNode: focusNodes[index],
                                keyboardType: TextInputType.number,
                                maxLength: index == 0 ? 6 : 1, // Allow 6 digits in first field for Android 15
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                                textAlign: TextAlign.center,
                                autofillHints: index == 0 ? [
                                  AutofillHints.oneTimeCode,
                                  'sms-otp-code',
                                  'one-time-code',
                                ] : null,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: isDark ? Colors.white : Colors.black87,
                                ),
                                decoration: InputDecoration(
                                  counterText: '',
                                  filled: true,
                                  fillColor: isDark ? Colors.grey[800] : Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide.none,
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: ColorPalette.primary,
                                      width: 2.0,
                                    ),
                                  ),
                                ),
                                onChanged: (value) {
                                  _handleOtpFieldChange(value, index);
                                },
                                onTap: () {
                                  controllers[index].selection = 
                                    TextSelection.fromPosition(
                                      TextPosition(
                                        offset: controllers[index].text.length,
                                      ),
                                    );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                          onPressed: () {
                            _clearOtpFields();
                            if (widget.onCancel != null) {
                              widget.onCancel!();
                            }
                            Navigator.pop(context, null);
                          },
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              fontFamily: ClientThemeManager()
                                  .currentClientConfig
                                  .fontFamily,
                            ),
                          ),
                        ),
                        ElevatedButton(
                          onPressed: _isValidating ? null : _onOtpSubmitted,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorPalette.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: _isValidating
                              ? const SizedBox(
                                  height: 16,
                                  width: 16,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2.0,
                                  ),
                                )
                              : Text(
                                  'Verify',
                                  style: TextStyle(
                                    fontFamily: ClientThemeManager()
                                        .currentClientConfig
                                        .fontFamily,
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
