import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/account_model.dart';
import '../../utils/services/account_manager.dart';
import '../../utils/services/transaction_service.dart';
import '../../utils/color_palette.dart';
import 'dashboard_screen.dart';
import '../../utils/services/shared_preferences_helper.dart';

import '../../utils/app_logger.dart';

class FullStatementScreen extends StatefulWidget {
  const FullStatementScreen({super.key});

  @override
  State<FullStatementScreen> createState() => _FullStatementScreenState();
}

class _FullStatementScreenState extends State<FullStatementScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  final TransactionService _transactionService = TransactionService();

  AccountModel? _selectedAccount;
  DateTime? _fromDate;
  DateTime? _toDate;
  bool _obscurePin = true;
  bool _isLoading = false;
  String? _successMessage;

  List<AccountModel> _accounts = [];

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      // Try cached accounts first, only fetch fresh if cache is empty
      List<dynamic>? accountList = await AccountManager.getCachedAccounts();

      if (accountList == null || accountList.isEmpty) {
        AppLogger.info(
            'No cached accounts found, fetching fresh accounts for FullStatementScreen');
        accountList = await AccountManager.fetchFreshAccounts();
      } else {
        AppLogger.info('Using cached accounts for FullStatementScreen');
      }

      setState(() {
        _accounts = accountList!
            .map((acc) {
              final List<String> actions = [];
              if (acc['canDeposit'] == 'Yes') actions.add('deposit');
              if (acc['canWithdraw'] == 'Yes') actions.add('withdraw');

              return AccountModel(
                name: acc['accountName'],
                balance: acc['balance'] ?? '0.00',
                accountNo: acc['accountNo'],
                allowedActions: actions,
                icon: Icons.account_balance_wallet,
                backgroundColor: ColorPalette.primary,
                rawAccount: acc,
              );
            })
            .where((acc) => (acc.rawAccount?['isLoanAccount'] ?? 'No') == 'No')
            .toList();
      });
    } catch (e) {
      AppLogger.error('loading accounts: $e');
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
        } else {
          _toDate = picked;
        }
      });
    }
  }

  void _onSubmit() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      _transactionService.processTransaction(
        serviceCode: 'AS',
        formData: {
          'accNo': _selectedAccount!.accountNo,
          'cmp': _pinController.text,
          'startDate': _fromDate,
          'endDate': _toDate,
          'emailAddress': _emailController.text,
        },
        context: context,
        statusCallback: (isLoading) {
          setState(() {
            _isLoading = isLoading;
          });
        },
        completedCallback: (response, success) {
          setState(() {
            _isLoading = false;
          });
          if (success) {
            setState(() {
              _successMessage =
                  response['header']['st'] ?? 'Statement sent successfully!';
            });
            _showSuccessDialog();
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    response['header']['sd'] ?? 'Failed to send statement'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        otpCallback: () async => null,
      );
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('yyyy-MM-dd').format(date);
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? Colors.black : ColorPalette.greyBackground,
      appBar: AppBar(
        backgroundColor: ColorPalette.secondary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: ColorPalette.textLight),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Full Statement',
          style: TextStyle(
            color: ColorPalette.textLight,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : Container(
              decoration: BoxDecoration(
                color: isDark ? Colors.grey[900] : ColorPalette.greyBackground,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Request full account statement',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'The statement will be sent to your registered email address.',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 32),
                      _buildAccountDropdown(isDark),
                      const SizedBox(height: 16),
                      _buildEmailField(isDark),
                      const SizedBox(height: 16),
                      _buildDatePickers(context, isDark),
                      const SizedBox(height: 16),
                      _buildPinField(isDark),
                      const SizedBox(height: 32),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _onSubmit,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorPalette.primary,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Request Statement',
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildAccountDropdown(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'From Account',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: ColorPalette.primary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[800] : ColorPalette.greyBackground,
            border: Border.all(
              color: isDark ? Colors.grey[700]! : Colors.grey.shade300,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              canvasColor:
                  isDark ? Colors.grey[800] : ColorPalette.greyBackground,
            ),
            child: DropdownButtonFormField<AccountModel>(
              value: _selectedAccount,
              icon: Icon(Icons.keyboard_arrow_down),
              decoration: InputDecoration(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                border: InputBorder.none,
                hintStyle: TextStyle(
                    color: isDark ? Colors.grey[400] : Colors.grey[600]),
              ),
              style: TextStyle(color: isDark ? Colors.white : Colors.black),
              hint: Text('Select account'),
              isExpanded: true,
              items: _accounts.map((AccountModel account) {
                return DropdownMenuItem<AccountModel>(
                  value: account,
                  child: Text(account.name),
                );
              }).toList(),
              onChanged: (newValue) {
                setState(() {
                  _selectedAccount = newValue;
                });
              },
              validator: (value) =>
                  value == null ? 'Please select an account' : null,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField(bool isDark) {
    return _buildFormField(
      label: 'Email Address',
      controller: _emailController,
      hint: 'Enter your email address',
      isDark: isDark,
      validator: (value) {
        if (value == null || value.isEmpty) return 'Please enter an email';
        if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value))
          return 'Please enter a valid email';
        return null;
      },
    );
  }

  Widget _buildDatePickers(BuildContext context, bool isDark) {
    return Row(
      children: [
        Expanded(
          child:
              _buildDatePicker(context, 'From Date', _fromDate, true, isDark),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildDatePicker(context, 'To Date', _toDate, false, isDark),
        ),
      ],
    );
  }

  Widget _buildDatePicker(BuildContext context, String label, DateTime? date,
      bool isFromDate, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: ColorPalette.primary,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDate(context, isFromDate),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : ColorPalette.greyBackground,
              border: Border.all(
                color: isDark ? Colors.grey[700]! : Colors.grey.shade300,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date == null ? 'Select Date' : _formatDate(date),
                  style:
                      TextStyle(color: isDark ? Colors.white : Colors.black87),
                ),
                Icon(Icons.calendar_today, color: Colors.grey),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPinField(bool isDark) {
    return _buildFormField(
      label: 'Enter PIN',
      controller: _pinController,
      hint: 'Your 4-digit PIN',
      isDark: isDark,
      isPin: true,
      obscureText: _obscurePin,
      onToggleObscure: () {
        setState(() {
          _obscurePin = !_obscurePin;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) return 'Please enter your PIN';
        if (value.length != 4) return 'PIN must be 4 digits';
        return null;
      },
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required bool isDark,
    bool isPin = false,
    bool obscureText = false,
    VoidCallback? onToggleObscure,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: ColorPalette.primary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          keyboardType: isPin ? TextInputType.number : TextInputType.text,
          maxLength: isPin ? 4 : null,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle:
                TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[600]),
            filled: true,
            fillColor: isDark ? Colors.grey[800] : ColorPalette.greyBackground,
            counterText: '',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: isDark ? Colors.grey[700]! : Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: isDark ? Colors.grey[700]! : Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: ColorPalette.primary),
            ),
            suffixIcon: isPin
                ? IconButton(
                    icon: Icon(
                        obscureText ? Icons.visibility_off : Icons.visibility),
                    onPressed: onToggleObscure,
                  )
                : null,
          ),
          validator: validator,
        ),
      ],
    );
  }

  void _showSuccessDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[900] : Colors.white,
            borderRadius: BorderRadius.circular(24),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.check_circle_outline,
                    color: Colors.green, size: 40),
              ),
              const SizedBox(height: 24),
              Text(
                'Statement Request Sent',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _successMessage ??
                    'Your statement has been sent to your registered email address.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    final userName =
                        await SharedPreferencesHelper.getCustomerName() ??
                            'User';
                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(
                          builder: (context) =>
                              DashboardScreen(userName: userName)),
                      (Route<dynamic> route) => false,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorPalette.primary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Done',
                      style: TextStyle(fontSize: 16, color: Colors.white)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
