import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../utils/color_palette.dart';
import 'mini_statement_screen.dart';
import 'transaction_receipt_screen.dart';
import '../../utils/services/auth_service.dart';
import '../../screens/security_questions_setup_screen.dart';
import '../../utils/sms_autofill_helper.dart';

import '../../utils/app_logger.dart';
class OtpVerificationScreen extends StatefulWidget {
  final String phoneNumber;
  final String transactionType;
  final String? amount;
  final Map<String, String>? transactionDetails;
  final AuthService? authService; // For login flow
  final Future<bool> Function(String otp)? onOtpVerified; // Callback for OTP verification

  const OtpVerificationScreen({
    super.key,
    required this.phoneNumber,
    required this.transactionType,
    this.amount,
    this.transactionDetails,
    this.authService, // Optional auth service for login flow
    this.onOtpVerified, // Optional callback for OTP verification
  });

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final List<TextEditingController> controllers =
      List.generate(4, (index) => TextEditingController());
  final List<FocusNode> focusNodes = List.generate(4, (index) => FocusNode());
  bool _isValidating = false;
  bool _isResendEnabled = false;
  bool _isResending = false;
  int _resendCountdown = 30;
  Timer? _resendTimer;
  String? _errorMessage; // Local error state
  String? _successMessage; // Local success state
  
  @override
  void initState() {
    super.initState();
    _startResendTimer();
    _setupAutofillListener();
    _requestSMSPermissions();
    AppLogger.info('OTP screen initialized with autofill enabled');
  }
  
  /// Request SMS permissions for Android 15+ compatibility
  Future<void> _requestSMSPermissions() async {
    try {
      // Log current autofill status
      SmsAutofillHelper.logAutofillDebugInfo('OTP Screen Init');
      
      // Request all necessary permissions
      final permissionsGranted = await SmsAutofillHelper.requestAutofillPermissions();
      
      if (permissionsGranted) {
        AppLogger.info('All SMS autofill permissions granted');
      } else {
        AppLogger.warning('Some SMS autofill permissions denied - autofill may not work on Android 15');
      }
      
      // Log final status
      final status = await SmsAutofillHelper.getAutofillStatus();
      AppLogger.info('Final SMS autofill status: $status');
      
    } catch (e) {
      AppLogger.error('Error requesting SMS permissions: $e');
    }
  }

  /// Setup autofill listener for SMS OTP
  void _setupAutofillListener() {
    // Focus on the first field to enable autofill detection
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        focusNodes[0].requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    for (var controller in controllers) {
      controller.dispose();
    }
    for (var node in focusNodes) {
      node.dispose();
    }
    super.dispose();
  }



  /// Fill OTP fields with the received code
  void _fillOtpFields(String code) {
    if (code.length == 4) {
      setState(() {
        for (int i = 0; i < 4; i++) {
          controllers[i].text = code[i];
        }
      });
      
      // Focus on the last field
      focusNodes[3].requestFocus();
      
      // Auto-submit after a brief delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _onOtpSubmitted();
        }
      });
    }
  }

  Future<void> _onOtpSubmitted() async {
    String otp = controllers.map((controller) => controller.text).join();
    if (otp.length != 4) {
      _showErrorSnackBar('Please enter a valid 4-digit OTP');
      return;
    }

    setState(() {
      _isValidating = true;
    });

    try {
      // Handle login flow with AuthService
      if (widget.transactionType == 'login' && widget.authService != null) {
        AuthResult result = await widget.authService!.validateOtp(widget.phoneNumber, otp, context);

        if (result.isSuccess) {
          Navigator.of(context).pop(true); // Return true to indicate successful login
        } else if (result.type == AuthResultType.securityQuestionsRequired) {
          // Navigate to security questions setup screen
          Navigator.of(context).pop(); // Close OTP dialog first
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const SecurityQuestionsSetupScreen(),
            ),
          );
        } else {
          _clearOtpFieldsOnly();
          _showErrorSnackBar(result.message);
        }
      }
      // Handle new callback-based OTP verification (for real transactions)
      else if (widget.onOtpVerified != null) {
        bool success = await widget.onOtpVerified!(otp);
        if (success) {
          Navigator.of(context).pop(otp); // Return the OTP string for transaction service
        } else {
          // Error handling is done in the callback, clear fields for retry
          _clearOtpFieldsOnly();
        }
      }
      // Handle other transaction types (legacy mock functionality)
      else {
        if (widget.transactionType == 'mini_statement') {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const MiniStatementScreen()),
          );
        } else {
          Map<String, dynamic> receiptData = {
            'Transaction ID': 'TXN${DateTime.now().millisecondsSinceEpoch}',
            'Transaction Type': widget.transactionType,
            'Date & Time': DateTime.now().toString().substring(0, 19),
            'Amount': widget.amount ?? '0',
            'Description': _getTransactionDescription(),
            'From Account': widget.transactionDetails?['account_type'] ?? 'Savings Account',
            'Status': 'Successful',
            'Charges': _calculateCharges(),
          };

          if (_getRecipientPhone().isNotEmpty) {
            receiptData['Recipient'] = _getRecipientPhone();
          }
          if (widget.transactionDetails?['to_account'] != null) {
            receiptData['To Account'] = widget.transactionDetails!['to_account'];
          }

          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => TransactionReceiptScreen(
                receiptData: receiptData,
                serviceCode: widget.transactionType,
              ),
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.info('OTP validation error: $e');
      _showErrorSnackBar('An error occurred during verification. Please try again.');
    } finally {
      setState(() {
        _isValidating = false;
      });
    }
  }

  /// Handle OTP field changes with smart navigation
  void _handleOtpFieldChange(String value, int index) {
    // Clear error message when user starts typing
    if (_errorMessage != null) {
      setState(() {
        _errorMessage = null;
      });
    }
    
    if (value.length > 1) {
      // Handle autofill or paste operation - distribute digits across fields
      _handlePastedOtp(value, index);
      return;
    }

    if (value.isNotEmpty) {
      // Move to next field when digit is entered
      if (index < 3) {
        focusNodes[index + 1].requestFocus();
      } else {
        // Auto-submit when last field is filled
        _checkAutoSubmit();
      }
    } else {
      // Handle backspace - move to previous field and clear it
      if (index > 0) {
        focusNodes[index - 1].requestFocus();
        // Clear the previous field when backspace is pressed on empty field
        controllers[index - 1].clear();
      }
    }
    
    // Trigger rebuild to update progress indicator
    setState(() {});
  }

  /// Handle pasted OTP codes
  void _handlePastedOtp(String pastedValue, int startIndex) {
    // Extract only digits from pasted content
    String digits = pastedValue.replaceAll(RegExp(r'[^0-9]'), '');
    
    // Clear all fields first
    for (int i = 0; i < 4; i++) {
      controllers[i].clear();
    }
    
    // Fill fields with pasted digits
    for (int i = 0; i < 4 && i < digits.length; i++) {
      controllers[i].text = digits[i];
    }
    
    // Move focus to the appropriate field
    if (digits.length >= 4) {
      focusNodes[3].requestFocus();
      _checkAutoSubmit();
    } else if (digits.isNotEmpty) {
      int nextIndex = digits.length < 4 ? digits.length : 3;
      focusNodes[nextIndex].requestFocus();
    }
    
    // Trigger rebuild to update progress indicator
    setState(() {});
  }

  /// Check if OTP is complete and auto-submit
  void _checkAutoSubmit() {
    String otp = controllers.map((controller) => controller.text).join();
    if (otp.length == 4) {
      // Small delay to show the complete OTP before submitting
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && !_isValidating) {
          _onOtpSubmitted();
        }
      });
    }
  }

  /// Clear all OTP fields
  void _clearOtpFields() {
    for (var controller in controllers) {
      controller.clear();
    }
    setState(() {
      _errorMessage = null; // Clear error message when fields are cleared
    });
    focusNodes[0].requestFocus();
  }

  /// Clear OTP fields without clearing error message
  void _clearOtpFieldsOnly() {
    for (var controller in controllers) {
      controller.clear();
    }
    focusNodes[0].requestFocus();
  }

  /// Start resend timer
  void _startResendTimer() {
    setState(() {
      _isResendEnabled = false;
      _resendCountdown = 30;
    });

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_resendCountdown > 0) {
          _resendCountdown--;
        } else {
          _isResendEnabled = true;
          timer.cancel();
        }
      });
    });
  }

  /// Resend OTP
  void _resendOtp() async {
    if (!_isResendEnabled || _isResending) return;

    setState(() {
      _isResending = true;
    });

    try {
      // For login flow, resend through auth service
      if (widget.transactionType == 'login' && widget.authService != null) {
        AuthResult result = await widget.authService!.resendOtp(widget.phoneNumber, context);
        
        if (result.isSuccess) {
          _showSuccessMessage(result.message);
          // Restart the timer after successful resend
          _startResendTimer();
        } else {
          _showErrorSnackBar(result.message);
        }
      } else {
        // For other transaction types, show placeholder message
        // This would need to be implemented based on specific transaction service
        _showSuccessMessage('OTP has been resent to ${widget.phoneNumber}');
        // Restart the timer after resend
        _startResendTimer();
      }
      
      // Clear existing OTP fields
      _clearOtpFields();
      
    } catch (e) {
      _showErrorSnackBar('Failed to resend OTP. Please try again.');
    } finally {
      setState(() {
        _isResending = false;
      });
    }
  }

  /// Show success message in the modal
  void _showSuccessMessage(String message) {
    setState(() {
      _errorMessage = null;
      _successMessage = message;
    });
    
    // Clear success message after 3 seconds
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _successMessage = null;
        });
      }
    });
  }


  /// Show error message in modal
  void _showErrorSnackBar(String message) {
    setState(() {
      _errorMessage = message;
    });
    
    // Clear error message after 4 seconds
    Timer(const Duration(seconds: 4), () {
      if (mounted) {
        setState(() {
          _errorMessage = null;
        });
      }
    });
  }

  /// Show info message via SnackBar
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  String _getTransactionDescription() {
    switch (widget.transactionType.toLowerCase()) {
      case 'withdraw':
        return 'Cash withdrawal from ${widget.transactionDetails?['account_type'] ?? 'account'}';
      case 'deposit':
        return 'Cash deposit to ${widget.transactionDetails?['account_type'] ?? 'account'}';
      case 'inter account':
        return 'Transfer from ${widget.transactionDetails?['account_type'] ?? 'account'} to ${widget.transactionDetails?['to_account'] ?? 'another account'}';
      case 'transfer to bank':
        String bankName = widget.transactionDetails?['bank'] ?? 'bank';
        String accountNumber = widget.transactionDetails?['bank_account'] ?? '';
        String accountHolder = widget.transactionDetails?['account_holder'] ?? '';
        if (accountHolder.isNotEmpty && accountNumber.isNotEmpty) {
          return 'Bank transfer to $accountHolder ($bankName - $accountNumber)';
        } else if (accountNumber.isNotEmpty) {
          return 'Bank transfer to $bankName account $accountNumber';
        } else {
          return 'Bank transfer to $bankName';
        }
      case 'pay to till':
        String tillNumber = widget.transactionDetails?['till_number'] ?? '';
        String tillOwner = widget.transactionDetails?['till_owner'] ?? '';
        if (tillOwner.isNotEmpty && tillNumber.isNotEmpty) {
          return 'Payment to $tillOwner (Till: $tillNumber)';
        } else if (tillNumber.isNotEmpty) {
          return 'Payment to till number $tillNumber';
        } else {
          return 'Payment to till';
        }
      case 'mpesa float':
        String agentNumber = widget.transactionDetails?['agent_number'] ?? '';
        String agentName = widget.transactionDetails?['agent_name'] ?? '';
        String storeNumber = widget.transactionDetails?['store_number'] ?? '';
        if (agentName.isNotEmpty && agentNumber.isNotEmpty) {
          if (storeNumber.isNotEmpty) {
            return 'Mpesa float purchase from $agentName (Agent: $agentNumber, Store: $storeNumber)';
          } else {
            return 'Mpesa float purchase from $agentName (Agent: $agentNumber)';
          }
        } else if (agentNumber.isNotEmpty && storeNumber.isNotEmpty) {
          return 'Mpesa float purchase for agent $agentNumber store $storeNumber';
        } else if (agentNumber.isNotEmpty) {
          return 'Mpesa float purchase for agent $agentNumber';
        } else {
          return 'Mpesa float purchase';
        }
      default:
        // Check if it's a utility bill payment
        if (widget.transactionType.toLowerCase().contains('payment')) {
          String utilityType = widget.transactionDetails?['utility_type'] ?? 'Utility';
          String utilityAccount = widget.transactionDetails?['utility_account'] ?? '';
          String fromAccount = widget.transactionDetails?['account_type'] ?? 'account';
          if (utilityAccount.isNotEmpty) {
            return '$utilityType payment from $fromAccount to account $utilityAccount';
          } else {
            return '$utilityType payment from $fromAccount';
          }
        }
        return 'Transaction completed successfully';
    }
  }

  String _getRecipientPhone() {
    switch (widget.transactionType.toLowerCase()) {
      case 'withdraw':
        return widget.transactionDetails?['recipient_phone'] ?? '';
      case 'deposit':
        return widget.transactionDetails?['recipient_phone'] ?? '';
      case 'inter account':
        return widget.transactionDetails?['recipient_phone'] ?? '';
      case 'transfer to bank':
        return widget.transactionDetails?['bank_account'] ?? '';
      case 'pay to till':
        return widget.transactionDetails?['till_number'] ?? '';
      case 'mpesa float':
        return widget.transactionDetails?['agent_number'] ?? '';
      default:
        // Check if it's a utility bill payment
        if (widget.transactionType.toLowerCase().contains('payment')) {
          return widget.transactionDetails?['utility_account'] ?? '';
        }
        return widget.transactionDetails?['recipient_phone'] ?? '';
    }
  }

  String? _calculateCharges() {
    if (widget.amount == null) return null;
    
    final amount = double.tryParse(widget.amount!) ?? 0;
    double charges = 0;
    
    // Simple charge calculation based on transaction type and amount
    switch (widget.transactionType.toLowerCase()) {
      case 'withdraw':
        charges = amount * 0.01; // 1% charge for withdrawals
        break;
      case 'inter account':
        charges = amount * 0.005; // 0.5% charge for inter account transfers
        break;
      case 'transfer to bank':
        charges = amount * 0.015; // 1.5% charge for bank transfers
        break;
      case 'pay to till':
        charges = amount * 0.008; // 0.8% charge for till payments
        break;
      case 'mpesa float':
        charges = amount * 0.012; // 1.2% charge for mpesa float
        break;
      case 'deposit':
        charges = 0; // No charges for deposits
        break;
      default:
        charges = 0; // No charges for other transaction types
        break;
    }
    
    return charges > 0 ? charges.toStringAsFixed(2) : null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: Dialog(
        insetPadding: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: isDark ? Colors.grey[900] : Colors.grey[100],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: ColorPalette.unselectedNavItemColor,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'OTP Verification',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Progress indicator
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(4, (index) {
                        String currentOtp = controllers.map((c) => c.text).join();
                        bool isFilled = index < currentOtp.length;
                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 2),
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: isFilled ? Colors.white : Colors.white.withValues(alpha: 0.3),
                            shape: BoxShape.circle,
                          ),
                        );
                      }),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Enter the OTP sent to ${widget.phoneNumber}',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[400] : Colors.grey[700],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    // Error message display
                    if (_errorMessage != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    // Success message display
                    if (_successMessage != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              color: Colors.green,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _successMessage!,
                                style: TextStyle(
                                  color: Colors.green,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    // SMS Autofill status indicator
                    FutureBuilder<PermissionStatus>(
                      future: Permission.sms.status,
                      builder: (context, snapshot) {
                        final hasPermission = snapshot.data?.isGranted ?? false;
                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: hasPermission 
                              ? Colors.green.withValues(alpha: 0.1)
                              : Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: hasPermission 
                                ? Colors.green.withValues(alpha: 0.3)
                                : Colors.orange.withValues(alpha: 0.3)
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                hasPermission ? Icons.message_rounded : Icons.message_outlined,
                                size: 16,
                                color: hasPermission ? Colors.green[600] : Colors.orange[600],
                              ),
                              const SizedBox(width: 6),
                              Text(
                                hasPermission ? 'SMS Auto-read enabled' : 'SMS Permission needed',
                                style: TextStyle(
                                  color: hasPermission ? Colors.green[600] : Colors.orange[600],
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Didn\'t receive the code?',
                          style: TextStyle(
                            color: isDark ? Colors.grey[400] : Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        TextButton(
                          onPressed: (_isResendEnabled && !_isResending && !_isValidating) ? _resendOtp : null,
                          child: _isResending
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 12,
                                      height: 12,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(ColorPalette.primary),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Resending...',
                                      style: TextStyle(
                                        color: ColorPalette.primary,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  _isResendEnabled ? 'Resend OTP' : 'Resend OTP ($_resendCountdown)',
                                  style: TextStyle(
                                    color: _isResendEnabled ? ColorPalette.primary : Colors.grey,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // SMS Autofill enabled OTP input
                    AutofillGroup(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(
                          4,
                          (index) => SizedBox(
                            width: 50,
                            child: KeyboardListener(
                              focusNode: FocusNode(),
                              onKeyEvent: (KeyEvent event) {
                                if (event is KeyDownEvent) {
                                  if (event.logicalKey == LogicalKeyboardKey.backspace) {
                                    if (controllers[index].text.isEmpty && index > 0) {
                                      focusNodes[index - 1].requestFocus();
                                      controllers[index - 1].clear();
                                    }
                                  }
                                }
                              },
                              child: TextField(
                                controller: controllers[index],
                                focusNode: focusNodes[index],
                                keyboardType: TextInputType.number,
                                maxLength: index == 0 ? 6 : 1, // Allow 6 digits in first field for Android 15
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                                textAlign: TextAlign.center,
                                autofillHints: index == 0 ? [
                                  AutofillHints.oneTimeCode,
                                  // Add additional autofill hints for Android 15
                                  'sms-otp-code',
                                  'one-time-code',
                                ] : null,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: isDark ? Colors.white : Colors.black87,
                                ),
                                decoration: InputDecoration(
                                  counterText: '',
                                  filled: true,
                                  fillColor: isDark ? Colors.grey[800] : Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide.none,
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: ColorPalette.primary,
                                      width: 2.0,
                                    ),
                                  ),
                                ),
                                onChanged: (value) {
                                  _handleOtpFieldChange(value, index);
                                },
                                onTap: () {
                                  // Clear the field when tapped for better UX
                                  controllers[index].selection = TextSelection.fromPosition(
                                    TextPosition(offset: controllers[index].text.length),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                          onPressed: () {
                            _clearOtpFields();
                            Navigator.pop(context);
                          },
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text('Cancel'),
                        ),
                        ElevatedButton(
                          onPressed: _isValidating ? null : _onOtpSubmitted,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorPalette.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: _isValidating
                              ? const SizedBox(
                                  height: 16,
                                  width: 16,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2.0,
                                  ),
                                )
                              : Text('Verify'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
