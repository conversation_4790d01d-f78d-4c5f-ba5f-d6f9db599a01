import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'providers/theme_provider.dart';
import 'providers/profile_image_provider.dart';
import 'screens/welcome_screen.dart';
import 'screens/login_screen.dart';
import 'configuration/client_config.dart';
import 'configuration/client_configurations.dart';
import 'utils/services/shared_preferences_helper.dart';
import 'utils/idle_timer.dart';
import 'utils/app_logger.dart';
import 'utils/font_helper.dart';

Future<void> main() async {
  await runZonedGuarded(() async {
    // Log app startup
    AppLogger.info('=== Starting app ===');
    AppLogger.info('CLIENT_ID from environment: ${const String.fromEnvironment('CLIENT_ID', defaultValue: 'not set')}');

    // Initialize Flutter bindings
    WidgetsFlutterBinding.ensureInitialized();

    // Set up the app
    await _initializeApp();

    final prefs = await SharedPreferences.getInstance();
    runApp(MyApp(prefs: prefs));
  }, (error, stack) {
    // Handle any errors
    if (kDebugMode) {
      AppLogger.error('Application startup error', error, stack);
    }
    // Run app with minimal setup as fallback
    try {
      WidgetsFlutterBinding.ensureInitialized();
      runApp(const MyApp(prefs: null));
    } catch (e) {
      // Last resort fallback
      runApp(const MyApp(prefs: null));
    }
  });
}

Future<void> _initializeApp() async {
  // 1. Load environment variables
  try {
    await dotenv.load(fileName: "assets/.env");
    AppLogger.info('Environment loaded successfully');
    AppLogger.debug('CLIENT_ID from .env: ${dotenv.get('CLIENT_ID', fallback: 'not found')}');
    AppLogger.debug('BASE_URL from .env: ${dotenv.get('BASE_URL', fallback: 'not found')}');
  } catch (e) {
    AppLogger.error('Error loading .env file', e);
    throw Exception('Failed to load environment configuration. Please ensure assets/.env exists.');
  }

  // 2. Set up system preferences
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 3. Initialize the client theme system
  await _initializeThemeSystem();
}

Future<void> _initializeThemeSystem() async {
  final manager = ClientThemeManager();

  // Register all available client configurations
  ClientConfigurations.registerAllClients();

  // Determine which client to use
  String clientId = await _determineClientId();

  // If it's a dynamic client ID, register it
  if (!['999', '38', '93', '116', '81', '120'].contains(clientId)) {
    ClientConfigurations.registerDynamicClient(clientId);
  }

  // Set the active client
  manager.setClient(clientId);
  manager.updateClientPaths();

  // Mark as initialized
  manager.markAsInitialized();

  // Save to SharedPreferences for persistence
  final prefs = await SharedPreferences.getInstance();
  await prefs.setString('CLIENT_ID', clientId);
  
  // Also save via SharedPreferencesHelper for the auth service
  await SharedPreferencesHelper.setClientId(clientId);

  AppLogger.info('Theme system initialized with client: $clientId');
}

Future<String> _determineClientId() async {
  // Priority order:
  // 1. Build argument (from --dart-define)
  // 2. Android BuildConfig (from flavor) 
  // 3. Environment variable from .env
  // 4. Default (999)

  // Check build argument first (from --dart-define)
  const buildArgClientId = String.fromEnvironment('CLIENT_ID', defaultValue: '');
  if (buildArgClientId.isNotEmpty) {
    AppLogger.info('Using CLIENT_ID from build argument: $buildArgClientId');
    return buildArgClientId;
  }

  // Check Android BuildConfig (from flavor)
  try {
    const platform = MethodChannel('sacco_config');
    final androidClientId = await platform.invokeMethod('getClientId');
    if (androidClientId != null && androidClientId.toString().isNotEmpty) {
      AppLogger.info('Using CLIENT_ID from Android BuildConfig: $androidClientId');
      return androidClientId.toString();
    }
  } catch (e) {
    AppLogger.warning('Error reading CLIENT_ID from Android BuildConfig', e);
  }

  // Check .env file (this should now always work since we have the file)
  final envClientId = dotenv.get('CLIENT_ID', fallback: '999');
  AppLogger.info('Using CLIENT_ID from .env: $envClientId');
  return envClientId;
}

class MyApp extends StatefulWidget {
  final SharedPreferences? prefs;

  const MyApp({super.key, required this.prefs});

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  late IdleTimerService _idleTimerService;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _idleTimerService = IdleTimerService(onTimeout: _onTimeout);
    // Timer will be enabled manually after a successful login.
    // _idleTimerService.enableIdleTimer(reason: 'App start');
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _idleTimerService.handleAppLifecycleState(state);
  }

  void _onTimeout() {
    final context = IdleTimerService.navigatorKey.currentContext;
    if (context != null) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (_) => const LoginScreen()),
        (route) => false,
      );
    }
  }

  /// Build TextTheme with the specified font family
  TextTheme _buildTextTheme(String fontFamily) {
    try {
      // Try to get the Google Font theme
      if (fontFamily == 'Inter' || fontFamily.contains('Inter')) {
        return GoogleFonts.interTextTheme();
      } else if (fontFamily == 'Roboto' || fontFamily.contains('Roboto')) {
        return GoogleFonts.robotoTextTheme();
      } else if (fontFamily == 'Poppins' || fontFamily.contains('Poppins')) {
        return GoogleFonts.poppinsTextTheme();
      } else if (fontFamily == 'Open Sans' || fontFamily.contains('OpenSans')) {
        return GoogleFonts.openSansTextTheme();
      } else if (fontFamily == 'Lato' || fontFamily.contains('Lato')) {
        return GoogleFonts.latoTextTheme();
      } else if (fontFamily == 'Source Sans Pro' || fontFamily.contains('SourceSansPro')) {
        return GoogleFonts.sourceSerif4TextTheme();
      } else if (fontFamily == 'Nunito Sans' || fontFamily.contains('NunitoSans')) {
        return GoogleFonts.nunitoSansTextTheme();
      } else {
        // Fallback to Inter if font not recognized
        return GoogleFonts.interTextTheme();
      }
    } catch (e) {
      // Fallback to default theme if Google Fonts fails
      return ThemeData.light().textTheme;
    }
  }

  /// Build dark TextTheme with the specified font family
  TextTheme _buildDarkTextTheme(String fontFamily) {
    try {
      final baseTheme = ThemeData.dark().textTheme;
      // Try to get the Google Font theme
      if (fontFamily == 'Inter' || fontFamily.contains('Inter')) {
        return GoogleFonts.interTextTheme(baseTheme);
      } else if (fontFamily == 'Roboto' || fontFamily.contains('Roboto')) {
        return GoogleFonts.robotoTextTheme(baseTheme);
      } else if (fontFamily == 'Poppins' || fontFamily.contains('Poppins')) {
        return GoogleFonts.poppinsTextTheme(baseTheme);
      } else if (fontFamily == 'Open Sans' || fontFamily.contains('OpenSans')) {
        return GoogleFonts.openSansTextTheme(baseTheme);
      } else if (fontFamily == 'Lato' || fontFamily.contains('Lato')) {
        return GoogleFonts.latoTextTheme(baseTheme);
      } else if (fontFamily == 'Source Sans Pro' || fontFamily.contains('SourceSansPro')) {
        return GoogleFonts.sourceSerif4TextTheme(baseTheme);
      } else if (fontFamily == 'Nunito Sans' || fontFamily.contains('NunitoSans')) {
        return GoogleFonts.nunitoSansTextTheme(baseTheme);
      } else {
        // Fallback to Inter if font not recognized
        return GoogleFonts.interTextTheme(baseTheme);
      }
    } catch (e) {
      // Fallback to default theme if Google Fonts fails
      return ThemeData.dark().textTheme;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get client configuration
    final clientConfig = ClientThemeManager().currentClientConfig;
    final colors = ClientThemeManager().colors;

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) {
            try {
              if (widget.prefs != null) {
                return ThemeProvider(widget.prefs!);
              } else {
                // Create a fallback theme provider that doesn't use SharedPreferences
                return ThemeProvider.fallback();
              }
            } catch (e) {
              return ThemeProvider.fallback();
            }
          }
        ),
        ChangeNotifierProvider(
          create: (_) {
            try {
              return ProfileImageProvider()..loadProfileImage();
            } catch (e) {
              return ProfileImageProvider();
            }
          }
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          try {
            // Define SystemUiOverlayStyle based on theme
            SystemUiOverlayStyle systemUiStyle;

            if (themeProvider.themeMode == ThemeMode.system) {
              // For system mode, let Flutter handle it automatically
              final brightness = MediaQuery.maybeOf(context)?.platformBrightness ?? Brightness.light;
              systemUiStyle = brightness == Brightness.dark
                  ? SystemUiOverlayStyle.light.copyWith(
                      systemNavigationBarColor: const Color(0xFF1E1E1E),
                      systemNavigationBarIconBrightness: Brightness.light,
                    )
                  : SystemUiOverlayStyle.dark.copyWith(
                      systemNavigationBarColor: colors.background,
                      systemNavigationBarIconBrightness: Brightness.dark,
                    );
            } else {
              systemUiStyle = themeProvider.themeMode == ThemeMode.dark
                  ? SystemUiOverlayStyle.light.copyWith(
                      systemNavigationBarColor: const Color(0xFF1E1E1E), // Dark background
                      systemNavigationBarIconBrightness: Brightness.light,
                    )
                  : SystemUiOverlayStyle.dark.copyWith(
                      systemNavigationBarColor: colors.background, // Use client background color
                      systemNavigationBarIconBrightness: Brightness.dark, // Dark icons for light background
                    );
            }

            return AnnotatedRegion<SystemUiOverlayStyle>(
              value: systemUiStyle, // Apply the style
              child: MaterialApp(
                navigatorKey: IdleTimerService.navigatorKey,
                builder: (context, child) {
                  return Listener(
                    onPointerDown: (_) {
                      _idleTimerService.resetTimer();
                    },
                    child: child!,
                  );
                },
                title: clientConfig.displayName,
                debugShowCheckedModeBanner: false,
                themeMode: themeProvider.themeMode,
                theme: ThemeData(
                  colorScheme: ColorScheme.fromSeed(
                    seedColor: colors.primary,
                    brightness: Brightness.light,
                  ).copyWith(
                    primary: colors.primary,
                    secondary: colors.secondary,
                    background: colors.background,
                    surface: colors.surface,
                    error: colors.error,
                  ),
                  useMaterial3: true,
                  scaffoldBackgroundColor: colors.background,
                  cardColor: colors.surface,
                  fontFamily: FontHelper.currentFontFamily,
                  textTheme: _buildTextTheme(FontHelper.currentFontFamily),
                  bottomNavigationBarTheme: BottomNavigationBarThemeData(
                    backgroundColor: colors.surface,
                  ),
                ),
                darkTheme: ThemeData(
                  colorScheme: ColorScheme.fromSeed(
                    seedColor: colors.primary,
                    brightness: Brightness.dark,
                  ).copyWith(
                    primary: colors.primary,
                    secondary: colors.secondary,
                    error: colors.error,
                  ),
                  useMaterial3: true,
                  scaffoldBackgroundColor: const Color(0xFF121212),
                  cardColor: const Color(0xFF1E1E1E),
                  fontFamily: FontHelper.currentFontFamily,
                  textTheme: _buildDarkTextTheme(FontHelper.currentFontFamily),
                  bottomNavigationBarTheme: const BottomNavigationBarThemeData(
                    backgroundColor: Color(0xFF1E1E1E),
                  ),
                  iconTheme: const IconThemeData(
                    color: Colors.white,
                  ),
                ),
                home: const WelcomeScreen(),
              ),
            );
          } catch (e) {
            // Fallback for any errors during theme building
            return MaterialApp(
              home: Scaffold(
                body: Center(
                  child: Text('An error occurred: $e'),
                ),
              ),
            );
          }
        },
      ),
    );
  }
}