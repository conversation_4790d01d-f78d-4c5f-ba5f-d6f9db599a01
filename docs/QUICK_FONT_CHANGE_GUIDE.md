# 🎨 Quick Font Change Guide

## How to Change Fonts for Any Client (with Hot Reload!)

### 1. Available Font Options:
```dart
FontHelper.interFontFamily      // Inter (recommended for fintech)
FontHelper.robotoFontFamily     // Roboto (Material Design)
FontHelper.poppinsFontFamily    // Poppins (modern, friendly)
GoogleFonts.lato().fontFamily!  // Lato (humanist)
GoogleFonts.openSans().fontFamily!  // Open Sans (readable)
GoogleFonts.nunitoSans().fontFamily! // Nunito Sans (rounded)
```

### 2. How to Change Fonts:

**Step 1:** Open `lib/configuration/client_configurations.dart`

**Step 2:** Find your client configuration (e.g., for Tangazoletu Sacco):
```dart
static void _registerDefaultClient(ClientThemeManager manager) {
  manager.registerClientConfig('999', ClientConfig(
    clientId: '999',
    displayName: 'Tangazoletu Sacco',
    // ... colors ...
    fontFamily: FontHelper.interFontFamily,  // 👈 CHANGE THIS LINE
    // ... rest of config ...
  ));
}
```

**Step 3:** Replace the `fontFamily` line with your desired font:
```dart
fontFamily: FontHelper.poppinsFontFamily,  // Now using Poppins!
```

**Step 4:** Hot reload! 🔥 Your entire app will instantly switch fonts.

### 3. Client Font Examples:

```dart
// Make Tangazoletu use Poppins
fontFamily: FontHelper.poppinsFontFamily,

// Make Mentor Cash use Lato  
fontFamily: GoogleFonts.lato().fontFamily!,

// Make Amica use Open Sans
fontFamily: GoogleFonts.openSans().fontFamily!,

// Keep Inter (current default)
fontFamily: FontHelper.interFontFamily,
```

### 4. Quick Client Reference:

| Client ID | Client Name | Method to Update |
|-----------|-------------|------------------|
| 999 | Tangazoletu Sacco | `_registerDefaultClient` |
| 38 | Mentor Cash | `_registerMentorClient` |
| 81 | Tower Sacco | `_registerTowerClient` |
| 116 | Amica Sacco | `_registerAmicaClient` |
| 120 | Magadi Sacco | `_registerMagadiClient` |
| 93 | M-BORESHA | `_registerMBoreshaClient` |

### 5. Font Recommendations by Use Case:

**💰 Financial Apps (Recommended):**
- `FontHelper.interFontFamily` - Modern, excellent for numbers
- `GoogleFonts.lato().fontFamily!` - Professional, trustworthy

**🎨 Modern/Friendly Apps:**
- `FontHelper.poppinsFontFamily` - Rounded, approachable
- `GoogleFonts.nunitoSans().fontFamily!` - Friendly, readable

**📱 Classic/Clean Apps:**
- `FontHelper.robotoFontFamily` - Material Design standard
- `GoogleFonts.openSans().fontFamily!` - Clean, readable

### 6. Test Different Fonts:
1. Change the font in the configuration
2. Hot reload
3. See the change instantly!
4. Keep the one you like best

**That's it!** You now have complete control over fonts per client with instant hot reload feedback! 🚀
