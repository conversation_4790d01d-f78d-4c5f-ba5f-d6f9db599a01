name: tangazo<PERSON>usacco
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  shared_preferences: ^2.2.2
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  permission_handler: ^11.0.1
  url_launcher: ^6.3.1
  local_auth: ^2.1.8
  smooth_page_indicator: ^1.1.0
  flutter_svg: ^2.0.9
  flutter_carousel_widget: ^2.1.2
  flutter_launcher_icons: ^0.14.3
  fl_chart: ^0.65.0
  flutter_animate: ^4.3.0
  image_picker: ^1.0.4
  font_awesome_flutter: ^10.7.0
  carousel_slider: ^5.0.0
  google_nav_bar: ^5.0.7
  flutter_contacts: ^1.1.9+2
  share_plus: ^7.2.2
  screenshot: ^3.0.0
  path_provider: ^2.1.1
  intl: ^0.19.0
  flutter_dotenv: ^5.1.0
  http: ^1.1.0
  device_info_plus: ^9.1.0
  synchronized: ^3.1.0
  jwt_decoder: ^2.0.1
  encrypt: ^5.0.1
  package_info_plus: ^4.2.0
  crypto: ^3.0.3
  image: ^4.0.17
  # otp_autofill: ^4.0.0  # Temporarily disabled to fix Android 11 issues
  logger: ^2.6.0
  google_fonts: ^6.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/38/
    - assets/images/93/
    - assets/images/116/
    - assets/images/120/
    - assets/images/999/
    - assets/icons/
    - assets/logos/
    - assets/logos/38/
    - assets/logos/93/
    - assets/logos/116/
    - assets/logos/120/
    - assets/logos/999/
    - assets_config/
    - assets/.env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
